#!/usr/bin/env python3
"""
测试重构后的SM2加密工具类
"""
import logging
from utils.sm2_utils import SM2Utils

# 设置日志级别为DEBUG以查看详细信息
logging.basicConfig(level=logging.DEBUG)

def test_refactored_sm2():
    """测试重构后的SM2加密"""
    print("=== 测试重构后的SM2加密工具类 ===")
    
    # 测试数据
    public_key = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEuuet5IYtshC+FxXAnTgCGPbU0yyozb9QNVwqLYejGRYBzKRWJmCEmcferOnSDSKt58tvJVugs7l1ILF62Z9y3A=="
    test_data = '{"user":"shs-xxaqbzylhy", "name":"许国杰", "orgCode":"数据处室", "orgName":"数据处室", "uuid":"test-uuid", "day":"20241201"}'
    
    print(f"公钥: {public_key[:50]}...")
    print(f"测试数据: {test_data}")
    
    # 测试公钥转换
    print("\n=== 测试公钥转换 ===")
    converted_key = SM2Utils.str_to_public_key(public_key)
    if converted_key:
        print(f"✅ 公钥转换成功: {converted_key[:50]}...")
    else:
        print("❌ 公钥转换失败")
    
    # 测试加密
    print("\n=== 测试加密 ===")
    encrypted_result = SM2Utils.encrypt_by_public_key(test_data, public_key)
    
    if encrypted_result:
        print(f"✅ 加密成功!")
        print(f"加密结果长度: {len(encrypted_result)}")
        print(f"加密结果: {encrypted_result[:100]}...")
        
        # 验证结果格式
        if encrypted_result.isupper() and all(c in '0123456789ABCDEF' for c in encrypted_result):
            print("✅ 结果格式正确：大写十六进制")
        else:
            print("⚠️ 结果格式可能不正确")
            
        return True
    else:
        print("❌ 加密失败")
        return False

def test_method_compatibility():
    """测试方法兼容性"""
    print("\n=== 测试方法兼容性 ===")
    
    # 检查方法是否存在
    methods_to_check = [
        'str_to_public_key',
        'encrypt_by_public_key', 
        'decrypt_by_private_key'
    ]
    
    for method_name in methods_to_check:
        if hasattr(SM2Utils, method_name):
            print(f"✅ 方法 {method_name} 存在")
        else:
            print(f"❌ 方法 {method_name} 不存在")

def compare_with_java_structure():
    """比较与Java结构的一致性"""
    print("\n=== 与Java结构对比 ===")
    
    # Java方法对应关系
    java_python_mapping = {
        'encryptByPublicKey': 'encrypt_by_public_key',
        'strToPublicKey': 'str_to_public_key'
    }
    
    print("Java方法 -> Python方法映射:")
    for java_method, python_method in java_python_mapping.items():
        if hasattr(SM2Utils, python_method):
            print(f"✅ {java_method} -> {python_method}")
        else:
            print(f"❌ {java_method} -> {python_method} (缺失)")

if __name__ == "__main__":
    test_method_compatibility()
    compare_with_java_structure()
    success = test_refactored_sm2()
    
    if success:
        print("\n🎉 重构测试完成，SM2工具类工作正常！")
    else:
        print("\n⚠️ 重构测试发现问题，请检查实现")
