#!/usr/bin/env python3
"""
测试SM2加密功能
"""
from gmssl import sm2
import base64

def test_sm2_encryption():
    """测试SM2加密"""
    # 测试公钥
    public_key = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEuuet5IYtshC+FxXAnTgCGPbU0yyozb9QNVwqLYejGRYBzKRWJmCEmcferOnSDSKt58tvJVugs7l1ILF62Z9y3A=="
    
    print("=== SM2加密测试 ===")
    print(f"公钥: {public_key[:50]}...")
    
    try:
        # 创建SM2对象
        print("\n1. 创建CryptSM2对象...")
        sm2_crypt = sm2.CryptSM2(public_key=public_key, private_key="")
        print("✅ CryptSM2对象创建成功")
        
        # 测试数据
        test_data = "Hello, SM2!"
        print(f"\n2. 测试数据: {test_data}")
        
        # 加密
        print("\n3. 开始加密...")
        encrypted_data = sm2_crypt.encrypt(test_data.encode('utf-8'))
        print(f"加密结果类型: {type(encrypted_data)}")
        print(f"加密结果: {encrypted_data}")
        
        if encrypted_data:
            hex_result = encrypted_data.hex()
            print(f"十六进制结果: {hex_result[:100]}...")
            print("✅ 加密成功")
        else:
            print("❌ 加密失败: 返回None")
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        print(f"错误类型: {type(e)}")

def test_public_key_format():
    """测试不同的公钥格式"""
    print("\n=== 公钥格式测试 ===")
    
    # 原始公钥
    public_key_base64 = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEuuet5IYtshC+FxXAnTgCGPbU0yyozb9QNVwqLYejGRYBzKRWJmCEmcferOnSDSKt58tvJVugs7l1ILF62Z9y3A=="
    
    try:
        # 解码base64
        decoded_key = base64.b64decode(public_key_base64)
        print(f"Base64解码后长度: {len(decoded_key)}")
        print(f"解码后数据: {decoded_key.hex()}")
        
        # 尝试提取公钥部分 (通常是最后64字节)
        if len(decoded_key) >= 64:
            # 跳过ASN.1头部，提取实际的公钥坐标
            public_key_coords = decoded_key[-64:]
            public_key_hex = public_key_coords.hex()
            print(f"提取的公钥坐标: {public_key_hex}")
            
            # 测试使用十六进制格式
            print("\n测试十六进制格式...")
            sm2_crypt = sm2.CryptSM2(public_key=public_key_hex, private_key="")
            result = sm2_crypt.encrypt("test".encode('utf-8'))
            print(f"十六进制格式加密结果: {type(result)}")
            
    except Exception as e:
        print(f"❌ 公钥格式测试失败: {e}")

if __name__ == "__main__":
    test_sm2_encryption()
    test_public_key_format()
