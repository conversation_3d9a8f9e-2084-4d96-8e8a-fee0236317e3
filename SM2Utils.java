package com.chinaunicom.datagov.util;


import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.io.*;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

/**
 * 国密非对称加密SM2工具类
 */
@Slf4j
public class SM2Utils {

    static final BouncyCastleProvider bc = new BouncyCastleProvider();

    /**
     * 加密方法
     * @param: data 需要加密的数据
     * @param: publicKeyStr 公钥
     * @return java.lang.String
     **/
    public static String encryptByPublicKey(String data, String publicKeyStr){
        SM2 sm2 = SmUtil.sm2();
        PublicKey publicKey = strToPublicKey(publicKeyStr);
        if(publicKey != null) {
            try {
                sm2.setPublicKey(publicKey);
                String s = sm2.encryptBcd(data, KeyType.PublicKey);
                return s;
            } catch (Exception e) {
                log.error("加密数据发生异常");
            }
        }
        return null;
    }

    /**
     * 从字符串中读取 公钥 key
     * @param publicKeyStr String
     * @return PublicKey
     */
    public  static PublicKey strToPublicKey(String publicKeyStr){
        PublicKey publicKey =  null;
        try {
            byte[] encPub = Base64.decode(publicKeyStr);
            KeyFactory keyFact = KeyFactory.getInstance("EC", bc);
            publicKey = keyFact.generatePublic(new X509EncodedKeySpec(encPub));
        }catch (Exception e){
            e.printStackTrace();
        }
        return publicKey;
    }
}