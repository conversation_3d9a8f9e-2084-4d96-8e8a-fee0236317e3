#!/usr/bin/env python3
"""
测试修复后的SM2加密
"""
from utils.sm2_utils import SM2Utils

def test_fixed_sm2():
    """测试修复后的SM2加密"""
    print("=== 测试修复后的SM2加密 ===")
    
    # 测试数据
    public_key = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEuuet5IYtshC+FxXAnTgCGPbU0yyozb9QNVwqLYejGRYBzKRWJmCEmcferOnSDSKt58tvJVugs7l1ILF62Z9y3A=="
    test_data = '{"user":"shs-xxaqbzylhy", "name":"许国杰", "orgCode":"数据处室", "orgName":"数据处室", "uuid":"test-uuid", "day":"20241201"}'
    
    print(f"公钥: {public_key[:50]}...")
    print(f"测试数据: {test_data}")
    
    # 测试加密
    print("\n开始加密...")
    encrypted_result = SM2Utils.encrypt_by_public_key(test_data, public_key)
    
    if encrypted_result:
        print(f"✅ 加密成功!")
        print(f"加密结果长度: {len(encrypted_result)}")
        print(f"加密结果: {encrypted_result[:100]}...")
        return True
    else:
        print("❌ 加密失败")
        return False

if __name__ == "__main__":
    success = test_fixed_sm2()
    if success:
        print("\n🎉 SM2加密修复成功!")
    else:
        print("\n❌ SM2加密仍有问题")
