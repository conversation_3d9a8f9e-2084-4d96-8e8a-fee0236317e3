import base64
from fastapi.background import P
from pyasn1.codec.der import decoder
from pyasn1.type import univ
from gmssl import sm2

def extract_raw_pubkey_from_x509_der(der_data):
    spki, _ = decoder.decode(der_data, asn1Spec=univ.Sequence())
    pubkey_bitstring = spki[1]
    pubkey_bytes = pubkey_bitstring.asOctets()
    return pubkey_bytes.hex().upper()

def encrypt_by_public_key(data: str, public_key_b64: str) -> str:
    # Step 1: Base64 解码 DER 数据
    der_data = base64.b64decode(public_key_b64.strip())

    # Step 2: 提取原始 SM2 公钥 Hex（x + y）
    raw_pubkey_hex = extract_raw_pubkey_from_x509_der(der_data)

    # Step 3: 使用 gmssl 加密
    cipher = sm2.CryptSM2(public_key=raw_pubkey_hex, private_key="")
    cipher_bytes = cipher.encrypt(data.encode('utf-8'))

    # Step 4: 返回 Hex 编码结果（与 Hutool 的 encryptBcd 一致）
    return cipher_bytes.hex().upper()

# ✅ 示例调用
if __name__ == '__main__':
    # 替换为你从 Java 获取的 Base64 编码的公钥
    public_key_b64 = """
    MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEuuet5IYtshC+FxXAnTgCGPbU0yyozb9QNVwqLYejGRYBzKRWJmCEmcferOnSDSKt58tvJVugs7l1ILF62Z9y3A==
    ...
    """  # ← 替换为你的完整 Base64 公钥字符串

    data = "Hello from Python"

    encrypted_hex = encrypt_by_public_key(data, public_key_b64)
    print("Encrypted (Hex):", encrypted_hex)