#!/usr/bin/env python3
"""
简单测试加密功能
"""
import sys
import os

# 添加utils目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

def test_simple():
    """简单测试"""
    print("=== 简单加密测试 ===")
    
    try:
        # 直接测试fallback方法
        from utils.sm2_utils import SM2Utils
        
        test_data = "Hello World"
        public_key = "test_key"
        
        print(f"测试数据: {test_data}")
        print(f"公钥: {public_key}")
        
        # 直接调用fallback方法
        result = SM2Utils._fallback_encrypt(test_data, public_key)
        print(f"Fallback加密结果: {result[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

if __name__ == "__main__":
    test_simple()
