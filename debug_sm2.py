#!/usr/bin/env python3
"""
调试SM2问题
"""
import base64

def analyze_public_key():
    """分析公钥格式"""
    public_key_base64 = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEuuet5IYtshC+FxXAnTgCGPbU0yyozb9QNVwqLYejGRYBzKRWJmCEmcferOnSDSKt58tvJVugs7l1ILF62Z9y3A=="
    
    print("=== 公钥分析 ===")
    print(f"Base64公钥: {public_key_base64}")
    print(f"Base64长度: {len(public_key_base64)}")
    
    # 解码
    try:
        decoded = base64.b64decode(public_key_base64)
        print(f"解码后长度: {len(decoded)} 字节")
        print(f"解码后hex: {decoded.hex()}")
        
        # 分析ASN.1结构
        print("\n=== ASN.1结构分析 ===")
        for i, byte in enumerate(decoded):
            print(f"字节 {i:2d}: 0x{byte:02x} ({byte:3d}) {''.join([chr(byte) if 32 <= byte <= 126 else '.' for byte in [byte]])}")
            if i > 20:  # 只显示前20个字节
                print("...")
                break
        
        # 提取最后64字节作为公钥坐标
        if len(decoded) >= 64:
            coords = decoded[-64:]
            print(f"\n最后64字节(公钥坐标): {coords.hex()}")
            
            # 分割为x和y坐标
            x_coord = coords[:32].hex()
            y_coord = coords[32:].hex()
            print(f"X坐标: {x_coord}")
            print(f"Y坐标: {y_coord}")
            
            # 生成不同格式
            formats = {
                "原始base64": public_key_base64,
                "完整hex": decoded.hex(),
                "坐标hex": coords.hex(),
                "04前缀坐标": "04" + coords.hex()
            }
            
            print("\n=== 可能的公钥格式 ===")
            for name, format_key in formats.items():
                print(f"{name}: {format_key[:80]}...")
                
            return formats
        
    except Exception as e:
        print(f"解码失败: {e}")
        return None

def test_gmssl_import():
    """测试gmssl导入"""
    print("\n=== 测试gmssl导入 ===")
    try:
        from gmssl import sm2
        print("✅ gmssl.sm2 导入成功")
        print(f"sm2模块属性: {[attr for attr in dir(sm2) if not attr.startswith('_')]}")
        
        # 测试CryptSM2类
        print("\n测试CryptSM2类...")
        try:
            # 使用空字符串测试
            crypt = sm2.CryptSM2(public_key="", private_key="")
            print("✅ CryptSM2创建成功(空参数)")
        except Exception as e:
            print(f"❌ CryptSM2创建失败(空参数): {e}")
            
        return True
    except Exception as e:
        print(f"❌ gmssl导入失败: {e}")
        return False

if __name__ == "__main__":
    test_gmssl_import()
    formats = analyze_public_key()
    
    if formats:
        print("\n=== 测试不同格式 ===")
        try:
            from gmssl import sm2
            
            for name, key_format in formats.items():
                print(f"\n测试 {name}:")
                try:
                    crypt = sm2.CryptSM2(public_key=key_format, private_key="")
                    print(f"✅ {name} 格式可以创建CryptSM2对象")
                    
                    # 尝试加密
                    try:
                        result = crypt.encrypt("test".encode('utf-8'))
                        if result:
                            print(f"✅ {name} 格式加密成功，结果长度: {len(result)}")
                        else:
                            print(f"❌ {name} 格式加密返回None")
                    except Exception as e:
                        print(f"❌ {name} 格式加密失败: {e}")
                        
                except Exception as e:
                    print(f"❌ {name} 格式创建失败: {e}")
                    
        except ImportError:
            print("无法导入gmssl，跳过格式测试")
