#!/usr/bin/env python3
"""
简单的SM2测试
"""
import base64
from gmssl import sm2

def test_key_formats():
    """测试不同的密钥格式"""
    
    # 原始base64公钥
    public_key_base64 = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEuuet5IYtshC+FxXAnTgCGPbU0yyozb9QNVwqLYejGRYBzKRWJmCEmcferOnSDSKt58tvJVugs7l1ILF62Z9y3A=="
    
    print("=== 测试不同的公钥格式 ===")
    
    # 1. 直接使用base64格式
    print("\n1. 测试base64格式:")
    try:
        sm2_crypt = sm2.CryptSM2(public_key=public_key_base64, private_key="")
        print("✅ base64格式创建成功")
    except Exception as e:
        print(f"❌ base64格式失败: {e}")
    
    # 2. 解码base64并转换为hex
    print("\n2. 测试hex格式:")
    try:
        decoded = base64.b64decode(public_key_base64)
        hex_key = decoded.hex()
        print(f"完整hex: {hex_key}")
        
        sm2_crypt = sm2.CryptSM2(public_key=hex_key, private_key="")
        print("✅ 完整hex格式创建成功")
    except Exception as e:
        print(f"❌ 完整hex格式失败: {e}")
    
    # 3. 提取公钥坐标部分
    print("\n3. 测试公钥坐标格式:")
    try:
        decoded = base64.b64decode(public_key_base64)
        # 跳过ASN.1头部，提取64字节的公钥坐标
        if len(decoded) >= 64:
            coords = decoded[-64:]
            coords_hex = coords.hex()
            print(f"坐标hex: {coords_hex}")
            
            sm2_crypt = sm2.CryptSM2(public_key=coords_hex, private_key="")
            print("✅ 坐标hex格式创建成功")
        else:
            print("❌ 解码后数据长度不足64字节")
    except Exception as e:
        print(f"❌ 坐标hex格式失败: {e}")
    
    # 4. 添加04前缀的坐标格式
    print("\n4. 测试04前缀坐标格式:")
    try:
        decoded = base64.b64decode(public_key_base64)
        if len(decoded) >= 64:
            coords = decoded[-64:]
            coords_with_prefix = "04" + coords.hex()
            print(f"04前缀坐标: {coords_with_prefix}")
            
            sm2_crypt = sm2.CryptSM2(public_key=coords_with_prefix, private_key="")
            print("✅ 04前缀坐标格式创建成功")
        else:
            print("❌ 解码后数据长度不足64字节")
    except Exception as e:
        print(f"❌ 04前缀坐标格式失败: {e}")

def test_encryption_with_working_format():
    """使用工作的格式测试加密"""
    print("\n=== 测试加密功能 ===")
    
    public_key_base64 = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEuuet5IYtshC+FxXAnTgCGPbU0yyozb9QNVwqLYejGRYBzKRWJmCEmcferOnSDSKt58tvJVugs7l1ILF62Z9y3A=="
    
    try:
        decoded = base64.b64decode(public_key_base64)
        coords = decoded[-64:]
        coords_with_prefix = "04" + coords.hex()
        
        sm2_crypt = sm2.CryptSM2(public_key=coords_with_prefix, private_key="")
        
        test_data = "Hello SM2"
        print(f"测试数据: {test_data}")
        
        encrypted = sm2_crypt.encrypt(test_data.encode('utf-8'))
        print(f"加密结果类型: {type(encrypted)}")
        
        if encrypted:
            print(f"加密成功，长度: {len(encrypted)}")
            print(f"十六进制: {encrypted.hex()[:100]}...")
            return True
        else:
            print("❌ 加密返回None")
            return False
            
    except Exception as e:
        print(f"❌ 加密测试失败: {e}")
        return False

if __name__ == "__main__":
    test_key_formats()
    test_encryption_with_working_format()
