import base64
from pyasn1.codec.der import decoder
from pyasn1.type import univ
from gmssl import sm2

def extract_raw_pubkey_from_x509_der(der_data):
    """
    从 DER 编码的 X.509 SubjectPublicKeyInfo 中提取原始 ECC 公钥（x+y）
    """
    spki, _ = decoder.decode(der_data, asn1Spec=univ.Sequence())
    pubkey_bitstring = spki[1]
    pubkey_bytes = pubkey_bitstring.asOctets()
    return pubkey_bytes.hex().upper()

# 替换为你从 Java 获取的 Base64 编码的公钥
public_key_b64 = """
MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEuuet5IYtshC+FxXAnTgCGPbU0yyozb9QNVwqLYejGRYBzKRWJmCEmcferOnSDSKt58tvJVugs7l1ILF62Z9y3A==
"""  # ← 替换为你自己的完整 Base64 字符串

try:
    # Step 1: Base64 解码
    der_data = base64.b64decode(public_key_b64.strip())

    # Step 2: 提取原始公钥 Hex（x + y）
    raw_pubkey_hex = extract_raw_pubkey_from_x509_der(der_data)
    print("Raw Public Key (Hex):", raw_pubkey_hex)

    # Step 3: 使用 gmssl 加密
    sm2_crypt = sm2.CryptSM2(public_key=raw_pubkey_hex, private_key="")

    data = "Hello from Python"
    cipher_bytes = sm2_crypt.encrypt(data.encode('utf-8'))

    # Step 4: 输出 Base64 编码的密文（与 Hutool 兼容）
    cipher_b64 = base64.b64encode(cipher_bytes).decode()
    print("Encrypted (Base64):", cipher_b64)

except Exception as e:
    print("Error:", str(e))